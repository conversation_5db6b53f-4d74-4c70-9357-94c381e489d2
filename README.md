# Învață Engleza - Aplicație Web pentru Învățarea Limbii Engleze

O aplicație web interactivă construită cu Python Flask pentru învățarea limbii engleze de la nivel începător până la avansat, cu funcționalități audio pentru pronunție.

## 🌟 Caracteristici

- **Trei nivele de dificultate**: Începător, Intermediar, Avansat
- **Lecții structurate** cu vocabular, gramatică și propoziții utile
- **Funcționalitate audio** pentru pronunția corectă a cuvintelor și propozițiilor
- **Exerciții interactive** cu diferite tipuri de întrebări:
  - Traduceri
  - Alegeri multiple
  - Completarea spațiilor libere
- **Tracking progres** - salvarea progresului utilizatorului
- **Interface responsive** cu Bootstrap 5
- **Design modern** cu animații și efecte vizuale

## 🚀 Instalare și Rulare

### Cerințe
- Python 3.7+
- pip (Python package manager)

### Pași de instalare

1. **Clonează sau descarcă proiectul**
   ```bash
   cd invata_engleza
   ```

2. **Instalează dependențele**
   ```bash
   pip install -r requirements.txt
   ```

3. **Rulează aplicația**
   ```bash
   python app.py
   ```

4. **Accesează aplicația**
   Deschide browserul și navighează la: `http://127.0.0.1:5000`

## 📁 Structura Proiectului

```
invata_engleza/
├── app.py                 # Aplicația Flask principală
├── requirements.txt       # Dependențele Python
├── README.md             # Documentația proiectului
├── data/
│   ├── lessons.json      # Conținutul lecțiilor
│   └── exercises.json    # Exercițiile pentru fiecare lecție
├── templates/
│   ├── base.html         # Template de bază
│   ├── index.html        # Pagina principală
│   ├── lesson.html       # Pagina lecției
│   └── exercise.html     # Pagina exercițiilor
└── static/
    ├── css/
    │   └── style.css     # Stiluri CSS personalizate
    └── js/
        └── app.js        # JavaScript pentru funcționalități interactive
```

## 🎯 Cum să Folosești Aplicația

### 1. Selectează Nivelul
- **Începător**: Salutări, numere, alfabetul, vocabular de bază
- **Intermediar**: Timpuri verbale, conversații, gramatică avansată
- **Avansat**: Structuri complexe, vocabular specializat, literatura

### 2. Studiază Lecțiile
- Fiecare lecție conține:
  - Vocabular nou cu pronunție fonetică
  - Explicații gramaticale cu exemple
  - Propoziții utile pentru conversații

### 3. Folosește Funcționalitatea Audio
- Apasă pe butoanele 🔊 pentru a auzi pronunția
- Folosește butonul "Redă tot vocabularul" pentru a asculta toate cuvintele
- Funcționalitatea folosește Web Speech API pentru pronunție naturală

### 4. Fă Exercițiile
- Testează-ți cunoștințele cu exerciții variate
- Primești feedback imediat pentru răspunsuri
- Urmărește-ți progresul în timp real

## 🔧 Funcționalități Tehnice

### Audio și Pronunție
- Folosește **Web Speech API** pentru text-to-speech
- Suport pentru voci engleze native
- Viteză ajustată pentru învățare (0.8x)
- Efecte vizuale în timpul pronunției

### Salvarea Progresului
- Progresul se salvează în sesiunea browserului
- Lecțiile finalizate sunt marcate vizual
- API endpoint pentru tracking progres

### Design Responsive
- Compatibil cu desktop, tabletă și mobil
- Bootstrap 5 pentru layout responsive
- Animații CSS pentru o experiență plăcută

## 🎨 Personalizare

### Adăugarea de Lecții Noi
Editează fișierul `data/lessons.json`:

```json
{
  "beginner": {
    "3": {
      "title": "Titlul Lecției",
      "description": "Descrierea lecției",
      "vocabulary": [
        {
          "english": "word",
          "romanian": "cuvânt",
          "phonetic": "/wɜːrd/"
        }
      ],
      "grammar": {
        "title": "Regula Gramaticală",
        "explanation": "Explicația regulii",
        "examples": [...]
      },
      "sentences": [...]
    }
  }
}
```

### Adăugarea de Exerciții
Editează fișierul `data/exercises.json`:

```json
{
  "beginner": {
    "3": {
      "title": "Titlul Exercițiului",
      "questions": [
        {
          "type": "translate",
          "romanian": "Text în română",
          "english": "english text"
        }
      ]
    }
  }
}
```

## 🌐 Suport Browser

Aplicația funcționează pe toate browserele moderne care suportă:
- HTML5
- CSS3
- JavaScript ES6+
- Web Speech API (pentru funcționalitatea audio)

### Browsere Testate
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 🔒 Securitate

- Aplicația folosește Flask cu configurații de securitate
- Sesiunile sunt securizate cu secret key
- Input-urile sunt validate și sanitizate
- Nu se stochează date personale

## 🚀 Dezvoltare Viitoare

### Funcționalități Planificate
- [ ] Sistem de conturi utilizator
- [ ] Statistici detaliate de progres
- [ ] Teste de nivel
- [ ] Certificări de finalizare
- [ ] Suport pentru mai multe limbi
- [ ] Aplicație mobilă

### Contribuții
Contribuțiile sunt binevenite! Pentru a contribui:
1. Fork proiectul
2. Creează o ramură pentru feature-ul tău
3. Commit modificările
4. Push la ramură
5. Deschide un Pull Request

## 📝 Licență

Acest proiect este licențiat sub MIT License - vezi fișierul LICENSE pentru detalii.

## 👨‍💻 Autor

Creat cu ❤️ pentru învățarea limbii engleze.

## 🆘 Suport

Dacă întâmpini probleme:
1. Verifică că ai Python 3.7+ instalat
2. Asigură-te că toate dependențele sunt instalate
3. Verifică că portul 5000 nu este ocupat
4. Pentru probleme audio, verifică că browserul suportă Web Speech API

---

**Mult succes la învățarea limbii engleze! 🎓**
