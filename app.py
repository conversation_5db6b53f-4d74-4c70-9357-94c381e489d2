from flask import Flask, render_template, request, jsonify, session
import json
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'

# Încarcă datele lec<PERSON>iilor
def load_lessons():
    try:
        with open('data/lessons.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def load_exercises():
    try:
        with open('data/exercises.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

@app.route('/')
def index():
    lessons = load_lessons()
    return render_template('index.html', lessons=lessons)

@app.route('/lesson/<level>/<int:lesson_id>')
def lesson(level, lesson_id):
    lessons = load_lessons()
    if level in lessons and str(lesson_id) in lessons[level]:
        lesson_data = lessons[level][str(lesson_id)]
        return render_template('lesson.html', lesson=lesson_data, level=level, lesson_id=lesson_id)
    return "Lecția nu a fost găsită", 404

@app.route('/exercise/<level>/<int:lesson_id>')
def exercise(level, lesson_id):
    exercises = load_exercises()
    if level in exercises and str(lesson_id) in exercises[level]:
        exercise_data = exercises[level][str(lesson_id)]
        return render_template('exercise.html', exercise=exercise_data, level=level, lesson_id=lesson_id)
    return "Exercițiul nu a fost găsit", 404

@app.route('/api/check_answer', methods=['POST'])
def check_answer():
    data = request.get_json()
    user_answer = data.get('answer', '').lower().strip()
    correct_answer = data.get('correct', '').lower().strip()
    
    is_correct = user_answer == correct_answer
    
    return jsonify({
        'correct': is_correct,
        'message': 'Corect!' if is_correct else f'Incorect. Răspunsul corect este: {correct_answer}'
    })

@app.route('/api/progress', methods=['POST'])
def save_progress():
    data = request.get_json()
    level = data.get('level')
    lesson_id = data.get('lesson_id')
    
    if 'progress' not in session:
        session['progress'] = {}
    
    if level not in session['progress']:
        session['progress'][level] = {}
    
    session['progress'][level][str(lesson_id)] = True
    session.permanent = True
    
    return jsonify({'status': 'success'})

@app.route('/api/get_progress')
def get_progress():
    return jsonify(session.get('progress', {}))

if __name__ == '__main__':
    # Creează directorul data dacă nu există
    if not os.path.exists('data'):
        os.makedirs('data')
    
    app.run(debug=True, host='0.0.0.0', port=5000)
