// JavaScript pentru aplicația de învățare a limbii engleze

// Verifică dacă browserul suportă Web Speech API
const speechSupported = 'speechSynthesis' in window;

// Funcție pentru pronunțarea textului
function speakText(text, lang = 'en-US') {
    if (!speechSupported) {
        alert('Browserul tău nu suportă funcția de pronunție audio.');
        return;
    }

    // Oprește orice pronunție în curs
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = lang;
    utterance.rate = 0.8; // Vitez<PERSON> mai lentă pentru învățare
    utterance.pitch = 1;
    utterance.volume = 1;

    // Găsește o voce engleză dacă este disponibilă
    const voices = speechSynthesis.getVoices();
    const englishVoice = voices.find(voice => 
        voice.lang.startsWith('en') && voice.name.includes('Female')
    ) || voices.find(voice => voice.lang.startsWith('en'));

    if (englishVoice) {
        utterance.voice = englishVoice;
    }

    // Adaugă efecte vizuale în timpul pronunției
    const speakButton = document.querySelector(`[data-text="${text}"]`);
    if (speakButton) {
        speakButton.classList.add('speaking');
        utterance.onend = () => {
            speakButton.classList.remove('speaking');
        };
        utterance.onerror = () => {
            speakButton.classList.remove('speaking');
        };
    }

    speechSynthesis.speak(utterance);
}

// Funcție pentru redarea unei liste de texte cu pauze
function playAllTexts(texts, index = 0) {
    if (index >= texts.length) return;

    const text = texts[index];
    if (!text) {
        playAllTexts(texts, index + 1);
        return;
    }

    speakText(text);

    // Așteaptă ca pronunția să se termine înainte de a continua
    const checkIfFinished = setInterval(() => {
        if (!speechSynthesis.speaking) {
            clearInterval(checkIfFinished);
            // Pauză de 1 secundă între cuvinte
            setTimeout(() => {
                playAllTexts(texts, index + 1);
            }, 1000);
        }
    }, 100);
}

// Funcție pentru marcarea unei lecții ca finalizată
function markLessonComplete(level, lessonId) {
    fetch('/api/progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            level: level,
            lesson_id: lessonId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const button = document.getElementById('mark-complete-btn');
            button.innerHTML = '<i class="fas fa-check me-2"></i>Lecție finalizată!';
            button.classList.remove('btn-info');
            button.classList.add('btn-success');
            button.disabled = true;
            
            // Afișează o notificare
            showNotification('Lecția a fost marcată ca finalizată!', 'success');
        }
    })
    .catch(error => {
        console.error('Eroare la salvarea progresului:', error);
        showNotification('Eroare la salvarea progresului.', 'error');
    });
}

// Funcție pentru afișarea notificărilor
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Elimină notificarea după 5 secunde
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Funcție pentru verificarea răspunsurilor în exerciții
function checkAnswer(userAnswer, correctAnswer) {
    return fetch('/api/check_answer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            answer: userAnswer,
            correct: correctAnswer
        })
    })
    .then(response => response.json());
}

// Funcție pentru încărcarea vocilor când sunt disponibile
function loadVoices() {
    return new Promise((resolve) => {
        let voices = speechSynthesis.getVoices();
        if (voices.length > 0) {
            resolve(voices);
        } else {
            speechSynthesis.onvoiceschanged = () => {
                voices = speechSynthesis.getVoices();
                resolve(voices);
            };
        }
    });
}

// Funcție pentru formatarea textului pentru pronunție
function formatTextForSpeech(text) {
    // Elimină caracterele speciale și formatează textul
    return text
        .replace(/[^\w\s.,!?-]/g, '')
        .replace(/\s+/g, ' ')
        .trim();
}

// Funcție pentru salvarea setărilor utilizatorului în localStorage
function saveUserSettings(settings) {
    localStorage.setItem('englishLearningSettings', JSON.stringify(settings));
}

// Funcție pentru încărcarea setărilor utilizatorului din localStorage
function loadUserSettings() {
    const settings = localStorage.getItem('englishLearningSettings');
    return settings ? JSON.parse(settings) : {
        speechRate: 0.8,
        speechPitch: 1,
        speechVolume: 1,
        autoPlay: false
    };
}

// Funcție pentru actualizarea progresului vizual
function updateProgressBar(current, total) {
    const percentage = Math.round((current / total) * 100);
    const progressBar = document.querySelector('.progress-bar');
    
    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.textContent = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
    }
}

// Funcție pentru animarea elementelor la scroll
function animateOnScroll() {
    const elements = document.querySelectorAll('.card, .alert');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    });

    elements.forEach(element => {
        observer.observe(element);
    });
}

// Funcție pentru gestionarea erorilor de pronunție
function handleSpeechError(error) {
    console.error('Eroare la pronunție:', error);
    showNotification('Eroare la redarea audio. Încearcă din nou.', 'error');
}

// Funcție pentru preîncărcarea vocilor
async function preloadVoices() {
    try {
        const voices = await loadVoices();
        console.log('Voci disponibile:', voices.length);
        
        // Găsește și setează vocea preferată
        const preferredVoice = voices.find(voice => 
            voice.lang.startsWith('en') && 
            (voice.name.includes('Female') || voice.name.includes('Google'))
        ) || voices.find(voice => voice.lang.startsWith('en'));

        if (preferredVoice) {
            console.log('Voce preferată:', preferredVoice.name);
        }
    } catch (error) {
        console.error('Eroare la încărcarea vocilor:', error);
    }
}

// Inițializare când documentul este încărcat
document.addEventListener('DOMContentLoaded', function() {
    // Preîncarcă vocile
    preloadVoices();
    
    // Inițializează animațiile
    animateOnScroll();
    
    // Încarcă setările utilizatorului
    const userSettings = loadUserSettings();
    
    // Adaugă event listeners pentru butoanele de pronunție
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('speak-btn') || e.target.closest('.speak-btn')) {
            const button = e.target.classList.contains('speak-btn') ? e.target : e.target.closest('.speak-btn');
            const text = button.getAttribute('data-text');
            if (text) {
                speakText(formatTextForSpeech(text));
            }
        }
    });

    // Adaugă funcționalitate pentru cuvintele cu click
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('word-english')) {
            const text = e.target.getAttribute('data-text') || e.target.textContent;
            if (text) {
                speakText(formatTextForSpeech(text));
            }
        }
    });

    // Gestionează formularul de căutare (dacă există)
    const searchForm = document.getElementById('search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = document.getElementById('search-input').value.trim();
            if (searchTerm) {
                searchLessons(searchTerm);
            }
        });
    }

    // Adaugă suport pentru tastatura
    document.addEventListener('keydown', function(e) {
        // Spațiu pentru redarea audio pe elementul focalizat
        if (e.code === 'Space' && e.target.classList.contains('speak-btn')) {
            e.preventDefault();
            e.target.click();
        }
        
        // Escape pentru oprirea pronunției
        if (e.code === 'Escape') {
            speechSynthesis.cancel();
        }
    });

    console.log('Aplicația de învățare a limbii engleze a fost inițializată cu succes!');
});

// Funcție pentru căutarea lecțiilor (dacă este implementată)
function searchLessons(searchTerm) {
    // Implementare pentru căutare
    console.log('Căutare pentru:', searchTerm);
}

// Export pentru utilizare în alte scripturi
window.EnglishLearningApp = {
    speakText,
    playAllTexts,
    markLessonComplete,
    showNotification,
    checkAnswer,
    loadUserSettings,
    saveUserSettings
};
