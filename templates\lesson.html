{% extends "base.html" %}

{% block title %}{{ lesson.title }} - Învață Engleza{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Acasă</a></li>
                <li class="breadcrumb-item">{{ level.title() }}</li>
                <li class="breadcrumb-item active">{{ lesson.title }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    {{ lesson.title }}
                </h4>
                <span class="badge bg-primary">{{ level.title() }}</span>
            </div>
            <div class="card-body">
                <p class="lead">{{ lesson.description }}</p>
                
                {% if lesson.vocabulary %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-list me-2"></i>
                        Vocabular
                    </h5>
                    <div class="row">
                        {% for word in lesson.vocabulary %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-light">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1 word-english" data-text="{{ word.english }}">
                                                {{ word.english }}
                                            </h6>
                                            <p class="mb-0 text-muted">{{ word.romanian }}</p>
                                            {% if word.phonetic %}
                                            <small class="text-info">{{ word.phonetic }}</small>
                                            {% endif %}
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm speak-btn" 
                                                data-text="{{ word.english }}" 
                                                title="Ascultă pronunția">
                                            <i class="fas fa-volume-up"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if lesson.grammar %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-cogs me-2"></i>
                        Gramatică
                    </h5>
                    <div class="card border-info">
                        <div class="card-body">
                            <h6>{{ lesson.grammar.title }}</h6>
                            <p>{{ lesson.grammar.explanation }}</p>
                            {% if lesson.grammar.examples %}
                            <h6>Exemple:</h6>
                            <ul class="list-unstyled">
                                {% for example in lesson.grammar.examples %}
                                <li class="mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="word-english" data-text="{{ example.english }}">
                                                {{ example.english }}
                                            </strong>
                                            <br>
                                            <small class="text-muted">{{ example.romanian }}</small>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm speak-btn" 
                                                data-text="{{ example.english }}" 
                                                title="Ascultă pronunția">
                                            <i class="fas fa-volume-up"></i>
                                        </button>
                                    </div>
                                </li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if lesson.sentences %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-comments me-2"></i>
                        Propoziții utile
                    </h5>
                    {% for sentence in lesson.sentences %}
                    <div class="card border-success mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="mb-1 word-english" data-text="{{ sentence.english }}">
                                        <strong>{{ sentence.english }}</strong>
                                    </p>
                                    <p class="mb-0 text-muted">{{ sentence.romanian }}</p>
                                </div>
                                <button class="btn btn-outline-success btn-sm speak-btn" 
                                        data-text="{{ sentence.english }}" 
                                        title="Ascultă pronunția">
                                    <i class="fas fa-volume-up"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Acțiuni
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" id="play-all-btn">
                        <i class="fas fa-play me-2"></i>
                        Redă tot vocabularul
                    </button>
                    <a href="{{ url_for('exercise', level=level, lesson_id=lesson_id) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-tasks me-2"></i>
                        Fă exercițiile
                    </a>
                    <button class="btn btn-info" id="mark-complete-btn" 
                            data-level="{{ level }}" 
                            data-lesson="{{ lesson_id }}">
                        <i class="fas fa-check me-2"></i>
                        Marchează ca finalizată
                    </button>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Sfaturi
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-volume-up text-primary me-2"></i>
                        Apasă pe butoanele audio pentru a auzi pronunția
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-repeat text-success me-2"></i>
                        Repetă cuvintele cu voce tare
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-pencil-alt text-warning me-2"></i>
                        Scrie cuvintele noi într-un caiet
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-tasks text-danger me-2"></i>
                        Fă exercițiile pentru a-ți testa cunoștințele
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Funcționalitate pentru redarea audio
    const speakButtons = document.querySelectorAll('.speak-btn');
    const playAllBtn = document.getElementById('play-all-btn');
    const markCompleteBtn = document.getElementById('mark-complete-btn');

    speakButtons.forEach(button => {
        button.addEventListener('click', function() {
            const text = this.getAttribute('data-text');
            speakText(text);
        });
    });

    if (playAllBtn) {
        playAllBtn.addEventListener('click', function() {
            const allTexts = Array.from(document.querySelectorAll('.word-english')).map(el => el.getAttribute('data-text'));
            playAllTexts(allTexts);
        });
    }

    if (markCompleteBtn) {
        markCompleteBtn.addEventListener('click', function() {
            const level = this.getAttribute('data-level');
            const lessonId = this.getAttribute('data-lesson');
            markLessonComplete(level, lessonId);
        });
    }
});
</script>
{% endblock %}
