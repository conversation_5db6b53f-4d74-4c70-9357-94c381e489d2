/* Stiluri personalizate pentru aplicația de învățare a limbii engleze */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Navbar personalizat */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Carduri pentru nivele */
.level-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

/* <PERSON><PERSON><PERSON> pentru pronunție */
.speak-btn {
    transition: all 0.3s ease;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.speak-btn:hover {
    transform: scale(1.1);
}

.speak-btn:active {
    transform: scale(0.95);
}

/* Animație pentru butoanele care vorbesc */
.speak-btn.speaking {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Stiluri pentru cuvintele cu audio */
.word-english {
    cursor: pointer;
    transition: color 0.3s ease;
}

.word-english:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Carduri pentru vocabular */
.card-body .card {
    transition: all 0.3s ease;
}

.card-body .card:hover {
    background-color: #f8f9fa;
    border-color: var(--primary-color);
}

/* Progres bar personalizat */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    transition: width 0.5s ease;
    font-weight: bold;
}

/* Stiluri pentru exerciții */
.exercise-question {
    transition: all 0.3s ease;
    background-color: white;
}

.exercise-question.answered {
    background-color: #f8f9fa;
    border-color: #28a745 !important;
}

.exercise-question:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Feedback pentru răspunsuri */
.answer-feedback {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stiluri pentru opțiuni multiple */
.form-check {
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.form-check:hover {
    background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label {
    font-weight: bold;
    color: var(--primary-color);
}

/* Jumbotron personalizat */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

/* Badge-uri personalizate */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
}

/* Butoane personalizate */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* Footer */
footer {
    margin-top: auto;
    background-color: var(--light-color) !important;
    border-top: 1px solid #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem !important;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .speak-btn {
        width: 35px;
        height: 35px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 8px 16px;
    }
}

/* Animații pentru încărcare */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Stiluri pentru breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
}

/* Stiluri pentru alerte */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Stiluri pentru input-uri */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Stiluri pentru liste */
.list-unstyled li {
    padding: 5px 0;
}

/* Hover effects pentru carduri */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stiluri pentru iconuri */
.fas, .far {
    transition: all 0.3s ease;
}

/* Stiluri pentru tabele (dacă sunt folosite) */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Stiluri pentru modal-uri (dacă sunt folosite) */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}
